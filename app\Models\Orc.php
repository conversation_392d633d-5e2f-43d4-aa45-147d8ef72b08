<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Orc
 *
 * @mixin IdeHelperOrc
 * @property int $id
 * @property string $number
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $begin_toot_number
 * @property int|null $end_toot_number
 * @property int $korpus_id
 * @property-read \App\Models\Korpus|null $korpus
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Toot> $toots
 * @property-read int|null $toots_count
 * @method static \Illuminate\Database\Eloquent\Builder|Orc newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Orc newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Orc query()
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereBeginTootNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereEndTootNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereKorpusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Orc whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Orc extends Model
{
    use HasFactory;

    const ID                  = 'id';
    const KORPUS_ID           = 'korpus_id';
    const NUMBER              = 'number';
    const BEGIN_TOOT_NUMBER   = 'begin_toot_number';
    const END_TOOT_NUMBER     = 'end_toot_number';
    const CODE                = 'code';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::KORPUS_ID,
        self::NUMBER,
        self::BEGIN_TOOT_NUMBER,
        self::END_TOOT_NUMBER,
        self::CODE
    ];

    public function korpus(): BelongsTo
    {
        return $this->belongsTo(Korpus::class);
    }

    public function toots()
    {
        return $this->hasMany(Toot::class);
    }
}
