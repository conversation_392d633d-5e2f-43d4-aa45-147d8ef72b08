<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Testing OrshinSuugch sync with fixed CVSecurity authentication...\n";

$uniquePhone = '887766' . rand(10, 99);
$uniqueEmail = 'syncfixed' . rand(100, 999) . '@test.com';
$os = \App\Models\OrshinSuugch::create([
    'name' => 'Test Sync Fixed',
    'phone' => $uniquePhone,
    'email' => $uniqueEmail
]);

echo "Created OrshinSuugch ID: " . $os->id . "\n";
echo "Check the logs and database for sync results.\n";

// Wait a moment for the sync to complete
sleep(2);

// Refresh the model to get updated data
$os->refresh();

echo "OrshinSuugch code after sync: " . ($os->code ?? 'NULL') . "\n";

if ($os->code) {
    echo "✅ SUCCESS: Sync completed successfully! Code: " . $os->code . "\n";
} else {
    echo "❌ FAILED: Sync did not complete or failed\n";
}
