<?php

namespace App\Services\CvSecurityService;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CvSecurityService
{
    /**
     * Check if CV Security service is available
     * Uses TCP connection check for ZKBio CVSecurity system
     *
     * @return bool
     */
    public function isServiceAvailable(): bool
    {
        try {
            $host = config('services.cv_security.host');
            $port = config('services.cv_security.port');

            if (!$host || !$port) {
                Log::warning('CV Security service configuration is incomplete');
                return false;
            }

            // For ZKBio CVSecurity system, use TCP connection check
            // This is more reliable than HTTP endpoints which require complex authentication
            $connection = @fsockopen($host, $port, $errno, $errstr, 5);

            if ($connection) {
                fclose($connection);
                return true;
            }

            Log::debug("CV Security TCP connection failed: {$errstr} ({$errno})");
            return false;

        } catch (\Exception $e) {
            Log::error('CV Security service health check failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get service status with details
     *
     * @return array
     */
    public function getServiceStatus(): array
    {
        $isAvailable = $this->isServiceAvailable();

        return [
            'service' => 'CV Security',
            'status' => $isAvailable ? 'online' : 'offline',
            'color' => $isAvailable ? 'success' : 'danger',
            'icon' => $isAvailable ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle',
            'host' => config('services.cv_security.host'),
            'port' => config('services.cv_security.port'),
        ];
    }

    /**
     * Placeholder method for future implementation
     * This service will be expanded with actual functionality in the future
     *
     * @return array
     */
    public function getInfo(): array
    {
        return [
            'service' => 'CV Security',
            'status' => 'placeholder',
            'message' => 'This service is ready for future implementation',
            'config' => [
                'host' => config('services.cv_security.host'),
                'port' => config('services.cv_security.port'),
                'has_api_key' => !empty(config('services.cv_security.api_key')),
            ]
        ];
    }

    /**
     * Create or update person in CVSecurity
     *
     * @param array $personData
     * @return object|null
     */
    public function createOrUpdatePerson(array $personData): ?object
    {
        try {
            $host = config('services.cv_security.host');
            $port = config('services.cv_security.port');
            $apiKey = config('services.cv_security.api_key');

            $url = "http://{$host}:{$port}/api/person/addPersonnelBasicInfo";

            $response = Http::withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'Authorization' => 'Bearer ' . $apiKey
                ])
                ->timeout(10)
                ->post($url, $personData);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            Log::error('CV Security createOrUpdatePerson failed', [
                'status' => $response->status(),
                'body' => $response->body(),
                'data' => $personData
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security createOrUpdatePerson exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $personData
            ]);
            return null;
        }
    }

    /**
     * Delete person from CVSecurity
     *
     * @param string $pin
     * @return object|null
     */
    public function deletePerson(string $pin): ?object
    {
        try {
            $host = config('services.cv_security.host');
            $port = config('services.cv_security.port');
            $apiKey = config('services.cv_security.api_key');

            $url = "http://{$host}:{$port}/api/person/delete/{$pin}";

            $response = Http::withToken($apiKey)
                ->post($url);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            Log::error('CV Security deletePerson failed', [
                'status' => $response->status(),
                'body' => $response->body(),
                'pin' => $pin
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security deletePerson exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get person from CVSecurity
     *
     * @param string $pin
     * @return object|null
     */
    public function getPerson(string $pin): ?object
    {
        try {
            $host = config('services.cv_security.host');
            $port = config('services.cv_security.port');
            $apiKey = config('services.cv_security.api_key');

            $url = "http://{$host}:{$port}/api/person/get/{$pin}";

            $response = Http::withToken($apiKey)
                ->get($url);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security getPerson exception: ' . $e->getMessage());
            return null;
        }
    }


}
