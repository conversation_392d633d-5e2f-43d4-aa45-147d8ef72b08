<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * App\Models\Toot
 *
 * @property int $id
 * @property int $number
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $korpus_id
 * @property-read \App\Models\Korpus|null $korpus
 * @method static \Illuminate\Database\Eloquent\Builder|Toot newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Toot newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Toot query()
 * @method static \Illuminate\Database\Eloquent\Builder|Toot whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Toot whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Toot whereKorpusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Toot whereNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Toot whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Toot extends Model
{
    use HasFactory;

    const ID        = 'id';
    const KORPUS_ID = 'korpus_id';
    const NUMBER    = 'number';

    protected $fillable = [
        self::ID,
        self::KORPUS_ID,
        self::NUMBER,
    ];

    public function korpus()
    {
        return $this->belongsTo(Korpus::class);
    }
}
