<?php

use App\Models\Sukh;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sukhs', function (Blueprint $table) {
            $table->string(Sukh::CODE)->nullable()->after(Sukh::BAG_ID);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sukhs', function (Blueprint $table) {
            $table->dropColumn(Sukh::CODE);
        });
    }
};
