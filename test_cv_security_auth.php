<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Testing CVSecurity service with access_token authentication...\n";

$cvService = app(\App\Services\CvSecurityService\CvSecurityService::class);
$isAvailable = $cvService->isServiceAvailable();

echo "CVSecurity service available: " . ($isAvailable ? 'YES' : 'NO') . "\n";

if ($isAvailable) {
    echo "Testing createOrUpdatePerson with access_token...\n";
    
    $personData = [
        'pin' => '999',
        'name' => 'Test Person',
        'lastName' => 'Test Last',
        'mobilePhone' => '99999999',
        'email' => '<EMAIL>'
    ];
    
    $response = $cvService->createOrUpdatePerson($personData);
    echo "Response: " . json_encode($response) . "\n";
    echo "Response type: " . gettype($response) . "\n";
    
    if ($response) {
        echo "SUCCESS: CVSecurity API is now working!\n";
    } else {
        echo "FAILED: Still getting null response\n";
    }
} else {
    echo "CVSecurity service is not available, cannot test createOrUpdatePerson\n";
}

echo "Configuration:\n";
echo "Host: " . config('services.cv_security.host') . "\n";
echo "Port: " . config('services.cv_security.port') . "\n";
echo "API Key: " . (config('services.cv_security.api_key') ? 'SET' : 'NOT SET') . "\n";
