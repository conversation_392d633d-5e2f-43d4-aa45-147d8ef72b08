<?php

namespace App\Services;

use App\Models\OrshinSuugch;
use App\Services\CvSecurityService\CvSecurityService;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use Illuminate\Support\Facades\Log;

/**
 * OrshinSuugch Synchronization Service
 *
 * This service handles the synchronization of OrshinSuugch (person) data
 * with the CVSecurity system. It follows the established pattern used by
 * other sync services in the application.
 */
class OrshinSuugchSyncService
{
    protected CvSecurityService $cvSecurityService;
    protected CvSecurityServiceExt $cvSecurityServiceExt;

    public function __construct(CvSecurityService $cvSecurityService, CvSecurityServiceExt $cvSecurityServiceExt)
    {
        $this->cvSecurityService = $cvSecurityService;
        $this->cvSecurityServiceExt = $cvSecurityServiceExt;
    }

    /**
     * Synchronize OrshinSuugch creation with CVSecurity
     *
     * @param OrshinSuugch $orshinSuugch
     * @return void
     */
    public function syncCreate(OrshinSuugch $orshinSuugch): void
    {
        try {
            Log::info('OrshinSuugchSyncService: Starting create sync', [
                'orshin_suugch_id' => $orshinSuugch->id,
                'name' => $orshinSuugch->name,
                'phone' => $orshinSuugch->phone
            ]);

            // Check if CVSecurity service is available
            if (!$this->cvSecurityService->isServiceAvailable()) {
                Log::warning('OrshinSuugchSyncService: CVSecurity service is not available for create sync', [
                    'orshin_suugch_id' => $orshinSuugch->id
                ]);
                return;
            }

            // Check if CVSecurity sync is enabled
            if (config('services.cv_security.sync_enabled', true) === false) {
                Log::info('OrshinSuugchSyncService: CVSecurity sync is disabled', [
                    'orshin_suugch_id' => $orshinSuugch->id
                ]);
                return;
            }

            // Get next available pin from CVSecurity EXT service
            $pin = $this->cvSecurityServiceExt->getNextPersonPin();
            if (!$pin) {
                Log::error('OrshinSuugchSyncService: Failed to get next person pin', [
                    'orshin_suugch_id' => $orshinSuugch->id
                ]);
                return;
            }

            // Prepare person data for CVSecurity
            $personData = $this->mapOrshinSuugchToPersonData($orshinSuugch, $pin);

            // Create person in CVSecurity
            $response = $this->cvSecurityService->createOrUpdatePerson($personData);

            if ($response) {
                // Extract pin from response and store it in the code field
                $code = $this->extractCodeFromResponse($response);

                if ($code) {
                    $orshinSuugch->update([OrshinSuugch::CODE => $code]);

                    Log::info('OrshinSuugchSyncService: Create sync successful', [
                        'orshin_suugch_id' => $orshinSuugch->id,
                        'cv_code' => $code
                    ]);
                } else {
                    Log::warning('OrshinSuugchSyncService: Could not extract code from response', [
                        'orshin_suugch_id' => $orshinSuugch->id,
                        'response' => $response
                    ]);
                }
            } else {
                Log::error('OrshinSuugchSyncService: Create sync failed', [
                    'orshin_suugch_id' => $orshinSuugch->id
                ]);
            }

        } catch (\Exception $e) {
            Log::error('OrshinSuugchSyncService: Create sync exception', [
                'orshin_suugch_id' => $orshinSuugch->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Synchronize OrshinSuugch update with CVSecurity
     *
     * @param OrshinSuugch $orshinSuugch
     * @return void
     */
    public function syncUpdate(OrshinSuugch $orshinSuugch): void
    {
        try {
            Log::info('OrshinSuugchSyncService: Starting update sync', [
                'orshin_suugch_id' => $orshinSuugch->id,
                'cv_code' => $orshinSuugch->code
            ]);

            // Check if CVSecurity service is available
            if (!$this->cvSecurityService->isServiceAvailable()) {
                Log::warning('OrshinSuugchSyncService: CVSecurity service is not available for update sync', [
                    'orshin_suugch_id' => $orshinSuugch->id
                ]);
                return;
            }

            // If no code exists, treat as create
            if (!$orshinSuugch->code) {
                Log::info('OrshinSuugchSyncService: No code found, treating update as create', [
                    'orshin_suugch_id' => $orshinSuugch->id
                ]);
                $this->syncCreate($orshinSuugch);
                return;
            }

            // Prepare person data for CVSecurity
            $personData = $this->mapOrshinSuugchToPersonData($orshinSuugch, $orshinSuugch->code);

            // Update person in CVSecurity
            $response = $this->cvSecurityService->createOrUpdatePerson($personData);

            if ($response) {
                Log::info('OrshinSuugchSyncService: Update sync successful', [
                    'orshin_suugch_id' => $orshinSuugch->id,
                    'cv_code' => $orshinSuugch->code
                ]);
            } else {
                Log::error('OrshinSuugchSyncService: Update sync failed', [
                    'orshin_suugch_id' => $orshinSuugch->id,
                    'cv_code' => $orshinSuugch->code
                ]);
            }

        } catch (\Exception $e) {
            Log::error('OrshinSuugchSyncService: Update sync exception', [
                'orshin_suugch_id' => $orshinSuugch->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Synchronize OrshinSuugch deletion with CVSecurity
     *
     * @param OrshinSuugch $orshinSuugch
     * @return void
     */
    public function syncDelete(OrshinSuugch $orshinSuugch): void
    {
        try {
            Log::info('OrshinSuugchSyncService: Starting delete sync', [
                'orshin_suugch_id' => $orshinSuugch->id,
                'cv_code' => $orshinSuugch->code
            ]);

            // Check if CVSecurity service is available
            if (!$this->cvSecurityService->isServiceAvailable()) {
                Log::warning('OrshinSuugchSyncService: CVSecurity service is not available for delete sync', [
                    'orshin_suugch_id' => $orshinSuugch->id
                ]);
                return;
            }

            // If no code exists, nothing to delete
            if (!$orshinSuugch->code) {
                Log::info('OrshinSuugchSyncService: No code found, skipping delete sync', [
                    'orshin_suugch_id' => $orshinSuugch->id
                ]);
                return;
            }

            // Delete person from CVSecurity
            $response = $this->cvSecurityService->deletePerson($orshinSuugch->code);

            if ($response) {
                Log::info('OrshinSuugchSyncService: Delete sync successful', [
                    'orshin_suugch_id' => $orshinSuugch->id,
                    'cv_code' => $orshinSuugch->code
                ]);
            } else {
                Log::error('OrshinSuugchSyncService: Delete sync failed', [
                    'orshin_suugch_id' => $orshinSuugch->id,
                    'cv_code' => $orshinSuugch->code
                ]);
            }

        } catch (\Exception $e) {
            Log::error('OrshinSuugchSyncService: Delete sync exception', [
                'orshin_suugch_id' => $orshinSuugch->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Map OrshinSuugch model to CVSecurity person data format
     *
     * @param OrshinSuugch $orshinSuugch
     * @param string $pin
     * @return array
     */
    protected function mapOrshinSuugchToPersonData(OrshinSuugch $orshinSuugch, string $pin): array
    {
        return [
            'pin' => $pin,
            'name' => $orshinSuugch->name ?? '',
            'lastName' => $orshinSuugch->last_name ?? '',
            'mobilePhone' => $orshinSuugch->phone ?? '',
            'email' => $orshinSuugch->email ?? '',
            // Add other fields as needed based on CVSecurity API requirements
        ];
    }

    /**
     * Extract code from CVSecurity response
     *
     * @param object $response
     * @return string|null
     */
    protected function extractCodeFromResponse(object $response): ?string
    {
        // Based on the API documentation, the response should contain the pin
        // This might need adjustment based on actual response format
        if (isset($response->data) && isset($response->data->pin)) {
            return $response->data->pin;
        }

        if (isset($response->pin)) {
            return $response->pin;
        }

        // If response format is different, log it for debugging
        Log::debug('OrshinSuugchSyncService: Unexpected response format', [
            'response' => $response
        ]);

        return null;
    }
}
